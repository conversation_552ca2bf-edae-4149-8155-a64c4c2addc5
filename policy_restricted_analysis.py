import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

# 1) Load only the active desktop sheets
active_sheets = ['EU Desktop', 'AU Desktop', 'US Desktop']
xls = pd.read_excel('/Users/<USER>/Downloads/Example.xlsx', sheet_name=active_sheets)
df = pd.concat([sheet.assign(region=region) for region, sheet in xls.items()],
               ignore_index=True)

print(f"✅ Loaded {len(active_sheets)} active desktop sheets:")
for sheet in active_sheets:
    count = len(xls[sheet])
    print(f"   - {sheet}: {count:,} domains")
print(f"📊 Total domains to analyze: {len(df):,}\n")

# Debug: Check available columns
print("📋 Available columns in the data:")
print(df.columns.tolist())
print()

# 2) Keyword‐based industry classification
def classify_domain(domain):
    d = str(domain).lower()
    if any(k in d for k in ['bet','casino','poker','gambling','bingo']):
        return 'Online Gambling'
    if any(k in d for k in ['wine','beer','vodka','whisky','alcohol']):
        return 'Alcohol'
    if any(k in d for k in ['pharma','pharmacy','drug','rx']):
        return 'Pharmaceutical'
    if any(k in d for k in ['bank','finance','loan','insurance','invest']):
        return 'Finance'
    if any(k in d for k in ['adult','porn','xxx','18+']):
        return 'Adult Content'
    return 'Other'

df['industry'] = df['domain'].apply(classify_domain)

# 3) Count of domains by Industry × Region
count_df = df.groupby(['region','industry']).size().reset_index(name='domain_count')
pivot_counts = count_df.pivot(index='region', columns='industry', values='domain_count').fillna(0).astype(int)
print("\nDomain Counts by Industry and Region:\n", pivot_counts)

# 4) Stacked‐bar of domain counts
regions = pivot_counts.index.tolist()
industries = pivot_counts.columns.tolist()
bottom = np.zeros(len(regions))

plt.figure(figsize=(8,5))
for industry in industries:
    vals = pivot_counts[industry].values
    plt.bar(regions, vals, bottom=bottom, label=industry)
    bottom += vals
plt.title('Number of Domains by Industry and Region')
plt.xlabel('Region')
plt.ylabel('Domain Count')
plt.legend(title='Industry', bbox_to_anchor=(1.05,1))
plt.tight_layout()
plt.savefig('domain_counts_by_industry_region.png', dpi=300, bbox_inches='tight')
plt.close()
print("✅ Saved: domain_counts_by_industry_region.png")

# 5) Scatter: Account Quality vs Low Volume Terms by Region and Industry
for region in df['region'].unique():
    sub = df[df['region']==region]
    plt.figure(figsize=(8,6))
    for industry in sub['industry'].unique():
        grp = sub[sub['industry']==industry]
        # Use available columns for meaningful analysis
        plt.scatter(grp['Account Quality'],
                    grp['Proportion Low Volume Terms'],
                    label=f'{industry} (n={len(grp)})',
                    alpha=0.7)
    plt.title(f'Account Quality vs Low Volume Terms by Industry—{region}')
    plt.xlabel('Account Quality')
    plt.ylabel('Proportion Low Volume Terms')
    plt.legend(title='Industry', bbox_to_anchor=(1.05,1))
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    filename = f'quality_vs_low_volume_{region.replace(" ", "_").replace("/", "_")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"✅ Saved: {filename}")

# 6) Additional analysis: Scrape frequency vs Coverage
for region in df['region'].unique():
    sub = df[df['region']==region]
    plt.figure(figsize=(8,6))
    for industry in sub['industry'].unique():
        grp = sub[sub['industry']==industry]
        plt.scatter(grp['Daily Avg. Scrapes per Search Term'],
                    grp['Daily Avg. Proportion of Missing Search Terms Observed with Ads (vs. 30-Day Max Coverage)'],
                    label=f'{industry} (n={len(grp)})',
                    alpha=0.7)
    plt.title(f'Scrape Frequency vs Missing Terms Coverage—{region}')
    plt.xlabel('Daily Avg. Scrapes per Search Term')
    plt.ylabel('Daily Avg. Proportion of Missing Search Terms')
    plt.legend(title='Industry', bbox_to_anchor=(1.05,1))
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    filename = f'scrapes_vs_coverage_{region.replace(" ", "_").replace("/", "_")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"✅ Saved: {filename}")
